package dashboard

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	incomeSourcesCollection     *mongo.Collection
	emergencyFundsCollection    *mongo.Collection
	investmentsCollection       *mongo.Collection
	assetsCollection            *mongo.Collection
	netWorthSnapshotsCollection *mongo.Collection
}

// New creates a new dashboard repository with MongoDB implementation
func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		incomeSourcesCollection:     db.Collection(repository.DASHBOARD_INCOME_SOURCES_COLLECTION),
		emergencyFundsCollection:    db.Collection(repository.DASHBOARD_EMERGENCY_FUNDS_COLLECTION),
		investmentsCollection:       db.Collection(repository.DASHBOARD_INVESTMENTS_COLLECTION),
		assetsCollection:            db.Collection(repository.DASHBOARD_ASSETS_COLLECTION),
		netWorthSnapshotsCollection: db.Collection(repository.DASHBOARD_NET_WORTH_SNAPSHOTS_COLLECTION),
	}

	// Create indexes
	repo.createIndexes()

	return repo
}

// createIndexes creates necessary indexes for dashboard collections
func (m *mongoDB) createIndexes() {
	ctx := context.Background()

	// Income Sources indexes
	_, err := m.incomeSourcesCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{Key: "userID", Value: 1}},
	})
	if err != nil {
		log.Printf("Warning: failed to create index on income sources userID field: %v", err)
	}

	// Emergency Funds indexes
	_, err = m.emergencyFundsCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys:    bson.D{{Key: "userID", Value: 1}},
		Options: options.Index().SetUnique(true),
	})
	if err != nil {
		log.Printf("Warning: failed to create unique index on emergency funds userID field: %v", err)
	}

	// Investments indexes
	_, err = m.investmentsCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{Key: "userID", Value: 1}},
	})
	if err != nil {
		log.Printf("Warning: failed to create index on investments userID field: %v", err)
	}

	// Assets indexes
	_, err = m.assetsCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{Key: "userID", Value: 1}},
	})
	if err != nil {
		log.Printf("Warning: failed to create index on assets userID field: %v", err)
	}

	// Net Worth Snapshots indexes
	_, err = m.netWorthSnapshotsCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{Key: "userID", Value: 1}, {Key: "date", Value: -1}},
	})
	if err != nil {
		log.Printf("Warning: failed to create index on net worth snapshots userID and date fields: %v", err)
	}
}

// IncomeSource operations
func (m *mongoDB) FindIncomeSource(ctx context.Context, id primitive.ObjectID) (*dashboard.IncomeSource, error) {
	var incomeSource dashboard.IncomeSource
	err := m.incomeSourcesCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&incomeSource)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "income source not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find income source", errors.Internal, err)
	}

	incomeSource.ID = incomeSource.ObjectID.Hex()
	return &incomeSource, nil
}

func (m *mongoDB) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	cursor, err := m.incomeSourcesCollection.Find(ctx, bson.M{"userID": userID})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find income sources", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var incomeSources []*dashboard.IncomeSource
	for cursor.Next(ctx) {
		var incomeSource dashboard.IncomeSource
		if err := cursor.Decode(&incomeSource); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode income source", errors.Internal, err)
		}
		incomeSource.ID = incomeSource.ObjectID.Hex()
		incomeSources = append(incomeSources, &incomeSource)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding income sources", errors.Internal, err)
	}

	return incomeSources, nil
}

func (m *mongoDB) CreateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error {
	result, err := m.incomeSourcesCollection.InsertOne(ctx, incomeSource)
	if err != nil {
		return errors.New(errors.Repository, "failed to create income source", errors.Internal, err)
	}

	incomeSource.ObjectID = result.InsertedID.(primitive.ObjectID)
	incomeSource.ID = incomeSource.ObjectID.Hex()
	return nil
}

func (m *mongoDB) UpdateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error {
	filter := bson.M{"_id": incomeSource.ObjectID}
	update := bson.M{"$set": incomeSource}

	result, err := m.incomeSourcesCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update income source", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "income source not found", errors.NotFound, nil)
	}

	return nil
}

func (m *mongoDB) DeleteIncomeSource(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.incomeSourcesCollection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete income source", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "income source not found", errors.NotFound, nil)
	}

	return nil
}

// EmergencyFund operations
func (m *mongoDB) FindEmergencyFund(ctx context.Context, userID string) (*dashboard.EmergencyFund, error) {
	var emergencyFund dashboard.EmergencyFund
	err := m.emergencyFundsCollection.FindOne(ctx, bson.M{"userID": userID}).Decode(&emergencyFund)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "emergency fund not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find emergency fund", errors.Internal, err)
	}

	emergencyFund.ID = emergencyFund.ObjectID.Hex()
	return &emergencyFund, nil
}

func (m *mongoDB) CreateEmergencyFund(ctx context.Context, emergencyFund *dashboard.EmergencyFund) error {
	result, err := m.emergencyFundsCollection.InsertOne(ctx, emergencyFund)
	if err != nil {
		return errors.New(errors.Repository, "failed to create emergency fund", errors.Internal, err)
	}

	emergencyFund.ObjectID = result.InsertedID.(primitive.ObjectID)
	emergencyFund.ID = emergencyFund.ObjectID.Hex()
	return nil
}

func (m *mongoDB) UpdateEmergencyFund(ctx context.Context, emergencyFund *dashboard.EmergencyFund) error {
	filter := bson.M{"_id": emergencyFund.ObjectID}
	update := bson.M{"$set": emergencyFund}

	result, err := m.emergencyFundsCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update emergency fund", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "emergency fund not found", errors.NotFound, nil)
	}

	return nil
}

// Investment operations
func (m *mongoDB) FindInvestment(ctx context.Context, id primitive.ObjectID) (*dashboard.Investment, error) {
	var investment dashboard.Investment
	err := m.investmentsCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&investment)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "investment not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find investment", errors.Internal, err)
	}

	investment.ID = investment.ObjectID.Hex()
	return &investment, nil
}

func (m *mongoDB) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	cursor, err := m.investmentsCollection.Find(ctx, bson.M{"userID": userID})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find investments", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var investments []*dashboard.Investment
	for cursor.Next(ctx) {
		var investment dashboard.Investment
		if err := cursor.Decode(&investment); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode investment", errors.Internal, err)
		}
		investment.ID = investment.ObjectID.Hex()
		investments = append(investments, &investment)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding investments", errors.Internal, err)
	}

	return investments, nil
}

func (m *mongoDB) CreateInvestment(ctx context.Context, investment *dashboard.Investment) error {
	result, err := m.investmentsCollection.InsertOne(ctx, investment)
	if err != nil {
		return errors.New(errors.Repository, "failed to create investment", errors.Internal, err)
	}

	investment.ObjectID = result.InsertedID.(primitive.ObjectID)
	investment.ID = investment.ObjectID.Hex()
	return nil
}

func (m *mongoDB) UpdateInvestment(ctx context.Context, investment *dashboard.Investment) error {
	filter := bson.M{"_id": investment.ObjectID}
	update := bson.M{"$set": investment}

	result, err := m.investmentsCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update investment", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "investment not found", errors.NotFound, nil)
	}

	return nil
}

func (m *mongoDB) DeleteInvestment(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.investmentsCollection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete investment", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "investment not found", errors.NotFound, nil)
	}

	return nil
}

// Asset operations
func (m *mongoDB) FindAsset(ctx context.Context, id primitive.ObjectID) (*dashboard.Asset, error) {
	var asset dashboard.Asset
	err := m.assetsCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&asset)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "asset not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find asset", errors.Internal, err)
	}

	asset.ID = asset.ObjectID.Hex()
	return &asset, nil
}

func (m *mongoDB) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	cursor, err := m.assetsCollection.Find(ctx, bson.M{"userID": userID})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find assets", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var assets []*dashboard.Asset
	for cursor.Next(ctx) {
		var asset dashboard.Asset
		if err := cursor.Decode(&asset); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode asset", errors.Internal, err)
		}
		asset.ID = asset.ObjectID.Hex()
		assets = append(assets, &asset)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding assets", errors.Internal, err)
	}

	return assets, nil
}

func (m *mongoDB) CreateAsset(ctx context.Context, asset *dashboard.Asset) error {
	result, err := m.assetsCollection.InsertOne(ctx, asset)
	if err != nil {
		return errors.New(errors.Repository, "failed to create asset", errors.Internal, err)
	}

	asset.ObjectID = result.InsertedID.(primitive.ObjectID)
	asset.ID = asset.ObjectID.Hex()
	return nil
}

func (m *mongoDB) UpdateAsset(ctx context.Context, asset *dashboard.Asset) error {
	filter := bson.M{"_id": asset.ObjectID}
	update := bson.M{"$set": asset}

	result, err := m.assetsCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update asset", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "asset not found", errors.NotFound, nil)
	}

	return nil
}

func (m *mongoDB) DeleteAsset(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.assetsCollection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete asset", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "asset not found", errors.NotFound, nil)
	}

	return nil
}

// NetWorthSnapshot operations
func (m *mongoDB) FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error) {
	opts := options.Find().SetSort(bson.D{{Key: "date", Value: -1}}).SetLimit(int64(limit))
	cursor, err := m.netWorthSnapshotsCollection.Find(ctx, bson.M{"userID": userID}, opts)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find net worth history", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var snapshots []*dashboard.NetWorthSnapshot
	for cursor.Next(ctx) {
		var snapshot dashboard.NetWorthSnapshot
		if err := cursor.Decode(&snapshot); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode net worth snapshot", errors.Internal, err)
		}
		snapshot.ID = snapshot.ObjectID.Hex()
		snapshots = append(snapshots, &snapshot)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding net worth history", errors.Internal, err)
	}

	return snapshots, nil
}

func (m *mongoDB) SaveNetWorthSnapshot(ctx context.Context, snapshot *dashboard.NetWorthSnapshot) error {
	result, err := m.netWorthSnapshotsCollection.InsertOne(ctx, snapshot)
	if err != nil {
		return errors.New(errors.Repository, "failed to save net worth snapshot", errors.Internal, err)
	}

	snapshot.ObjectID = result.InsertedID.(primitive.ObjectID)
	snapshot.ID = snapshot.ObjectID.Hex()
	return nil
}
