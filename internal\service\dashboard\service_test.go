package dashboard

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Mock repository
type MockDashboardRepository struct {
	mock.Mock
}

func (m *MockDashboardRepository) FindIncomeSource(ctx context.Context, id primitive.ObjectID) (*dashboard.IncomeSource, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.IncomeSource), args.Error(1)
}

func (m *MockDashboardRepository) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.IncomeSource), args.Error(1)
}

func (m *MockDashboardRepository) CreateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error {
	args := m.Called(ctx, incomeSource)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error {
	args := m.Called(ctx, incomeSource)
	return args.Error(0)
}

func (m *MockDashboardRepository) DeleteIncomeSource(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindEmergencyFund(ctx context.Context, userID string) (*dashboard.EmergencyFund, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.EmergencyFund), args.Error(1)
}

func (m *MockDashboardRepository) CreateEmergencyFund(ctx context.Context, emergencyFund *dashboard.EmergencyFund) error {
	args := m.Called(ctx, emergencyFund)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateEmergencyFund(ctx context.Context, emergencyFund *dashboard.EmergencyFund) error {
	args := m.Called(ctx, emergencyFund)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindInvestment(ctx context.Context, id primitive.ObjectID) (*dashboard.Investment, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.Investment), args.Error(1)
}

func (m *MockDashboardRepository) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.Investment), args.Error(1)
}

func (m *MockDashboardRepository) CreateInvestment(ctx context.Context, investment *dashboard.Investment) error {
	args := m.Called(ctx, investment)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateInvestment(ctx context.Context, investment *dashboard.Investment) error {
	args := m.Called(ctx, investment)
	return args.Error(0)
}

func (m *MockDashboardRepository) DeleteInvestment(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindAsset(ctx context.Context, id primitive.ObjectID) (*dashboard.Asset, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.Asset), args.Error(1)
}

func (m *MockDashboardRepository) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.Asset), args.Error(1)
}

func (m *MockDashboardRepository) CreateAsset(ctx context.Context, asset *dashboard.Asset) error {
	args := m.Called(ctx, asset)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateAsset(ctx context.Context, asset *dashboard.Asset) error {
	args := m.Called(ctx, asset)
	return args.Error(0)
}

func (m *MockDashboardRepository) DeleteAsset(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error) {
	args := m.Called(ctx, userID, limit)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.NetWorthSnapshot), args.Error(1)
}

func (m *MockDashboardRepository) SaveNetWorthSnapshot(ctx context.Context, snapshot *dashboard.NetWorthSnapshot) error {
	args := m.Called(ctx, snapshot)
	return args.Error(0)
}

// Mock financial sheet service
type MockFinancialSheetService struct {
	mock.Mock
}

func (m *MockFinancialSheetService) FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, year int, month int) ([]*financialsheet.Transaction, error) {
	args := m.Called(ctx, userID, categoryType, year, month)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*financialsheet.Transaction), args.Error(1)
}

func (m *MockFinancialSheetService) FindCategoryByIdentifier(ctx context.Context, identifier string) (*financialsheet.Category, error) {
	args := m.Called(ctx, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

// Test suite
type DashboardServiceTestSuite struct {
	suite.Suite
	service                   Service
	mockRepo                  *MockDashboardRepository
	mockFinancialSheetService *MockFinancialSheetService
	testUserID                string
}

func (suite *DashboardServiceTestSuite) SetupTest() {
	suite.mockRepo = new(MockDashboardRepository)
	suite.mockFinancialSheetService = new(MockFinancialSheetService)
	suite.service = New(suite.mockRepo, suite.mockFinancialSheetService)
	suite.testUserID = "test-user-123"
}

func TestDashboardServiceTestSuite(t *testing.T) {
	suite.Run(t, new(DashboardServiceTestSuite))
}

func (suite *DashboardServiceTestSuite) TestCreateIncomeSource() {
	ctx := context.Background()

	suite.mockRepo.On("CreateIncomeSource", ctx, mock.AnythingOfType("*dashboard.IncomeSource")).Return(nil)

	incomeSource, err := suite.service.CreateIncomeSource(ctx, suite.testUserID, "Salary", 5000, byte(financialsheet.MoneySourceOpt1))

	suite.Require().NoError(err)
	suite.Equal("Salary", incomeSource.Name)
	suite.Equal(monetary.Amount(5000), incomeSource.MonthlyAmount)
	suite.Equal(suite.testUserID, incomeSource.UserID)
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestCreateIncomeSource_ValidationError() {
	ctx := context.Background()

	// Test with empty name
	_, err := suite.service.CreateIncomeSource(ctx, suite.testUserID, "", 5000, byte(financialsheet.MoneySourceOpt1))

	suite.Error(err)
	suite.Contains(err.Error(), "income source name is required")
}

func (suite *DashboardServiceTestSuite) TestUpdateEmergencyFund_CreateNew() {
	ctx := context.Background()

	// Mock repository to return NotFound error (emergency fund doesn't exist)
	notFoundErr := errors.New(errors.Repository, "emergency fund not found", errors.NotFound, nil)
	suite.mockRepo.On("FindEmergencyFund", ctx, suite.testUserID).Return(nil, notFoundErr)
	suite.mockRepo.On("CreateEmergencyFund", ctx, mock.AnythingOfType("*dashboard.EmergencyFund")).Return(nil)

	err := suite.service.UpdateEmergencyFund(ctx, suite.testUserID, 10000)

	suite.Require().NoError(err)
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestUpdateEmergencyFund_UpdateExisting() {
	ctx := context.Background()

	existingFund := &dashboard.EmergencyFund{
		ObjectID:     primitive.NewObjectID(),
		UserID:       suite.testUserID,
		CurrentValue: 5000,
		GoalValue:    50000,
	}

	suite.mockRepo.On("FindEmergencyFund", ctx, suite.testUserID).Return(existingFund, nil)
	suite.mockRepo.On("UpdateEmergencyFund", ctx, mock.AnythingOfType("*dashboard.EmergencyFund")).Return(nil)

	err := suite.service.UpdateEmergencyFund(ctx, suite.testUserID, 15000)

	suite.Require().NoError(err)
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestCreateMonthlySnapshot() {
	ctx := context.Background()

	// Mock data
	emergencyFund := &dashboard.EmergencyFund{
		UserID:       suite.testUserID,
		CurrentValue: 10000,
		GoalValue:    50000,
	}

	investments := []*dashboard.Investment{
		{UserID: suite.testUserID, Name: "Investment 1", CurrentValue: 15000},
		{UserID: suite.testUserID, Name: "Investment 2", CurrentValue: 10000},
	}

	assets := []*dashboard.Asset{
		{UserID: suite.testUserID, Description: "Car", Value: 80000},
	}

	// Setup mocks
	suite.mockRepo.On("FindEmergencyFund", ctx, suite.testUserID).Return(emergencyFund, nil)
	suite.mockRepo.On("FindInvestments", ctx, suite.testUserID).Return(investments, nil)
	suite.mockRepo.On("FindAssets", ctx, suite.testUserID).Return(assets, nil)
	suite.mockRepo.On("SaveNetWorthSnapshot", ctx, mock.AnythingOfType("*dashboard.NetWorthSnapshot")).Return(nil)

	err := suite.service.CreateMonthlySnapshot(ctx, suite.testUserID)

	suite.Require().NoError(err)
	suite.mockRepo.AssertExpectations(suite.T())
}

func TestIsNotFoundError(t *testing.T) {
	// Test with NotFound error
	notFoundErr := errors.New(errors.Repository, "not found", errors.NotFound, nil)
	assert.True(t, isNotFoundError(notFoundErr))

	// Test with other error
	internalErr := errors.New(errors.Repository, "internal error", errors.Internal, nil)
	assert.False(t, isNotFoundError(internalErr))

	// Test with non-domain error
	regularErr := assert.AnError
	assert.False(t, isNotFoundError(regularErr))
}
